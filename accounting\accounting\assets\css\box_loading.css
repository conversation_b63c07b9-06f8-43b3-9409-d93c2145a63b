#box-loading{
  padding:0px;
  margin:0px;
  box-sizing:border-box;
  font-family:'Poppins', sans-serif;
  perspective:800px;
  justify-content:center;
  align-items:center;
  position: fixed;
  top: 50%;
  left: 50%;
  margin: 0;
  transform: translate(-50%,-50%);
  z-index: 888;
}
::selection{
  background-color:#2d98da;
  color:#fff;
}
.Box{
  position:relative;
  width:80px;
  height:80px;
  background-color:none;
  border-radius:50%;
  border:5px solid transparent;
  border-top:5px solid #45aaf2;
  margin:2px;
  animation:Loading 2s linear infinite;
}
.Box span{
  position:absolute;
  width:100%;
  height:100%;
  top:0px;
  left:0px;
  border:5px solid transparent;
  border-top:5px solid #45aaf2;
  border-radius:50%;
  margin:2px;
  animation:Loading 2s linear infinite;
}



@keyframes Loading{
  0%{
    transform:rotate(0deg);
  }
  100%{
    transform:rotate(360deg);
  }
}
